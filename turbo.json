{"$schema": "https://turbo.build/schema.json", "globalDependencies": ["**/.env.*local"], "globalEnv": ["NODE_ENV", "PORT", "NEXT_PUBLIC_SITE_URL", "VERCEL_URL", "LEMONSQUEEZY_WEBHOOK_SECRET", "LEMONSQUEEZY_API_KEY", "STRIPE_SECRET_KEY", "STRIPE_WEBHOOK_SECRET", "MAIL_HOST", "MAIL_PORT", "MAIL_USER", "MAIL_PASS", "GITHUB_CLIENT_ID", "GITHUB_CLIENT_SECRET", "GOOGLE_CLIENT_ID", "GOOGLE_CLIENT_SECRET", "NEXT_PUBLIC_SUPABASE_URL"], "pipeline": {"build": {"dependsOn": ["^db:generate", "^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "check-types": {}, "clean": {"cache": false}, "db:generate": {"cache": false}, "db:push": {"cache": false}, "dev": {"cache": false, "dependsOn": ["^db:generate"], "persistent": true}, "export": {"outputs": ["out/**"]}, "lint": {}}}