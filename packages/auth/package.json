{"dependencies": {"@lucia-auth/adapter-prisma": "^4.0.0", "arctic": "^1.2.1", "database": "workspace:*", "lucia": "^3.0.1", "mail": "workspace:*", "next": "14.1.1", "oslo": "^1.1.3", "utils": "workspace:*", "zod": "^3.22.2"}, "devDependencies": {"@types/node": "^20.11.24", "@types/react": "18.2.61", "eslint-config-custom": "workspace:*", "tsconfig": "workspace:*"}, "license": "MIT", "main": "./index.tsx", "name": "auth", "scripts": {"lint": "eslint \"**/*.ts*\""}, "types": "./**/.tsx", "version": "0.0.0"}