import type {
  CancelSubscription,
  CreateCheckoutLink,
  CreateCustomerPortalLink,
  GetAllPlans,
  PauseSubscription,
  ResumeSubscription,
  SubscriptionPlan,
} from "../types";

import * as stripeProvider from "./stripe";
import * as lemonsqueezyProvider from "./lemonsqueezy";

const hasStripe = Boolean(process.env.STRIPE_SECRET_KEY);
const hasLemonsqueezy = Boolean(process.env.LEMONSQUEEZY_API_KEY);

const activeProvider = hasLemonsqueezy
  ? lemonsqueezyProvider
  : hasStripe
  ? stripeProvider
  : null;

export const getAllPlans: GetAllPlans = async () => {
  if (activeProvider) {
    return activeProvider.getAllPlans();
  }

  return Promise.resolve([] as SubscriptionPlan[]);
};

export const createCheckoutLink: CreateCheckoutLink = async (params) => {
  if (activeProvider) {
    return activeProvider.createCheckoutLink(params);
  }

  throw new Error("Billing provider not configured");
};

export const createCustomerPortalLink: CreateCustomerPortalLink = async (
  params,
) => {
  if (activeProvider) {
    return activeProvider.createCustomerPortalLink(params);
  }

  throw new Error("Billing provider not configured");
};

export const pauseSubscription: PauseSubscription = async (params) => {
  if (activeProvider) {
    return activeProvider.pauseSubscription(params);
  }

  throw new Error("Billing provider not configured");
};

export const cancelSubscription: CancelSubscription = async (params) => {
  if (activeProvider) {
    return activeProvider.cancelSubscription(params);
  }

  throw new Error("Billing provider not configured");
};

export const resumeSubscription: ResumeSubscription = async (params) => {
  if (activeProvider) {
    return activeProvider.resumeSubscription(params);
  }

  throw new Error("Billing provider not configured");
};