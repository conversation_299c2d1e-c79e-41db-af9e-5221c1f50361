{"dependencies": {"@hookform/resolvers": "^3.3.4", "@node-rs/argon2": "^1.8.0", "@node-rs/bcrypt": "^1.10.0", "@radix-ui/react-accordion": "^1.1.2", "@radix-ui/react-alert-dialog": "^1.0.5", "@radix-ui/react-avatar": "^1.0.4", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-dropdown-menu": "^2.0.6", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-select": "^2.0.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-tabs": "^1.0.4", "@radix-ui/react-toast": "^1.1.5", "@radix-ui/react-tooltip": "^1.0.7", "@tanstack/react-query": "^5.24.2", "@tanstack/react-table": "^8.13.2", "@trpc/client": "11.0.0-next-beta.303", "@trpc/react-query": "11.0.0-next-beta.303", "api": "workspace:*", "boring-avatars": "^1.10.1", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "contentlayer": "^0.3.2", "cypress": "^13.6.6", "database": "workspace:*", "date-fns": "^3.3.1", "deepmerge": "^4.3.1", "geist": "^1.2.2", "jotai": "^2.7.0", "js-cookie": "^3.0.5", "lucide-react": "^0.344.0", "mail": "workspace:*", "next": "14.1.1", "next-contentlayer": "^0.3.2", "next-intl": "3.9.1", "next-themes": "^0.2.1", "nextjs-toploader": "^1.6.6", "react": "18.2.0", "react-cropper": "^2.3.3", "react-day-picker": "^8.10.0", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.50.1", "sharp": "^0.33.2", "superjson": "^2.2.1", "tailwind-merge": "^2.2.1", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^2.15.1", "utils": "workspace:*", "uuid": "^9.0.1", "zod": "^3.22.2"}, "devDependencies": {"@types/cookie": "^0.6.0", "@types/js-cookie": "^3.0.4", "@types/node": "20.11.24", "@types/react": "18.2.61", "@types/react-dom": "18.2.19", "@types/uuid": "^9.0.8", "auth": "workspace:*", "autoprefixer": "10.4.17", "dotenv-cli": "^7.3.0", "eslint-config-custom": "workspace:*", "postcss": "8.4.35", "start-server-and-test": "^2.0.3", "tailwind-config": "workspace:*", "tailwindcss": "3.4.1", "tsconfig": "workspace:*"}, "name": "web", "private": true, "scripts": {"build": "next build", "dev": "next dev", "e2e": "dotenv -c -e ../../.env -- start-server-and-test dev http://localhost:3000 \"cypress open --e2e\"", "lint": "next lint", "shadcn-ui": "pnpm dlx shadcn-ui@latest", "start": "next start"}, "version": "0.1.0"}