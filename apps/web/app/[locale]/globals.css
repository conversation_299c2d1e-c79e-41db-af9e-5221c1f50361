@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }

  /* Typography improvements */
  h1, h2, h3, h4, h5, h6 {
    @apply font-serif;
  }

  .prose h1, .prose h2, .prose h3, .prose h4, .prose h5, .prose h6 {
    @apply font-serif;
  }
}

@layer utilities {
  /* Typography utilities */
  .font-decorative {
    @apply font-decorative;
  }

  /* Heading styles with serif font */
  .heading-xl {
    @apply font-serif text-4xl md:text-5xl lg:text-6xl font-bold leading-tight;
  }

  .heading-lg {
    @apply font-serif text-3xl md:text-4xl font-semibold leading-tight;
  }

  .heading-md {
    @apply font-serif text-2xl md:text-3xl font-semibold leading-snug;
  }

  .heading-sm {
    @apply font-serif text-xl md:text-2xl font-medium leading-snug;
  }

  /* Body text improvements */
  .text-body-lg {
    @apply text-lg leading-relaxed;
  }

  .text-body {
    @apply text-base leading-relaxed;
  }

  .text-body-sm {
    @apply text-sm leading-relaxed;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
}
