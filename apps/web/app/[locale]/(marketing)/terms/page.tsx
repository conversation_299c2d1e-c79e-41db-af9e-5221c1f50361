import { getTranslations } from "next-intl/server";

export default async function TermsPage() {
  const t = await getTranslations();
  return (
    <div className="container max-w-3xl py-12">
      <h1 className="text-4xl font-bold mb-4">{t("legal.terms.title")}</h1>
      <p className="opacity-70 mb-6">{t("legal.terms.subtitle")}</p>
      <div className="prose dark:prose-invert">
        <h2>{t("legal.common.introduction")}</h2>
        <p>{t("legal.terms.introduction")}</p>
        <h2>{t("legal.common.userResponsibilities")}</h2>
        <p>{t("legal.terms.userResponsibilities")}</p>
        <h2>{t("legal.common.limitations")}</h2>
        <p>{t("legal.terms.limitations")}</p>
      </div>
    </div>
  );
}