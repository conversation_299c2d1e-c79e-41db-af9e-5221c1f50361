import { getTranslations } from "next-intl/server";

export default async function PrivacyPage() {
  const t = await getTranslations();
  return (
    <div className="container max-w-3xl py-12">
      <h1 className="text-4xl font-bold mb-4">{t("legal.privacy.title")}</h1>
      <p className="opacity-70 mb-6">{t("legal.privacy.subtitle")}</p>
      <div className="prose dark:prose-invert">
        <h2>{t("legal.common.introduction")}</h2>
        <p>{t("legal.privacy.introduction")}</p>
        <h2>{t("legal.common.dataCollection")}</h2>
        <p>{t("legal.privacy.dataCollection")}</p>
        <h2>{t("legal.common.dataUsage")}</h2>
        <p>{t("legal.privacy.dataUsage")}</p>
      </div>
    </div>
  );
}