import { getTranslations } from "next-intl/server";

export default async function ContactPage() {
  const t = await getTranslations();
  return (
    <div className="container max-w-3xl py-12">
      <h1 className="text-4xl font-bold mb-4">{t("legal.contact.title")}</h1>
      <p className="opacity-70 mb-6">{t("legal.contact.subtitle")}</p>
      <div className="prose dark:prose-invert">
        <p>{t("legal.contact.description")}</p>
        <p>
          {t("legal.contact.emailLabel")}: <a href="mailto:<EMAIL>"><EMAIL></a>
        </p>
      </div>
    </div>
  );
}