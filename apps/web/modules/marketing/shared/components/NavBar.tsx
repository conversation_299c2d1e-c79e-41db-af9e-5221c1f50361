"use client";

import { Link } from "@i18n";
import { useUser } from "@saas/auth/hooks/use-user";
import { ColorModeToggle } from "@shared/components/ColorModeToggle";
import { LocaleSwitch } from "@shared/components/LocaleSwitch";
import { Logo } from "@shared/components/Logo";
import { Button } from "@ui/components/button";
import { Icon } from "@ui/components/icon";
import { Sheet, SheetContent, SheetTrigger } from "@ui/components/sheet";
import { useLocale, useTranslations } from "next-intl";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import { useIsClient } from "usehooks-ts";
import { Banner } from "./Banner";

export function NavBar() {
  const t = useTranslations();
  const { user, loaded: userLoaded } = useUser();
  const locale = useLocale();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const isClient = useIsClient();

  useEffect(() => {
    setMobileMenuOpen(false);
  }, [pathname]);

  const menuItems = [
    {
      label: t("common.menu.pricing"),
      href: `/pricing`,
      icon: Icon.creditCard,
    },
    {
      label: t("common.menu.blog"),
      href: "/blog",
      icon: Icon.docs,
    },
  ];

  return (
    <nav
      className={`bg-background/80 fixed left-0 top-0 z-20 w-full backdrop-blur-lg`}
      data-test="navigation"
    >
      <Banner />

      <div className="container">
        <div className="flex items-center justify-stretch gap-6 py-8">
          <div className="flex flex-1 justify-start">
            <Link
              href="/"
              className="block hover:no-underline active:no-underline"
            >
              <Logo />
            </Link>
          </div>

          <div className="hidden flex-1 items-center justify-center md:flex">
            {menuItems.map((menuItem) => (
              <Link
                key={menuItem.href}
                href={menuItem.href}
                className={`flex items-center gap-2 px-3 py-2 text-lg font-medium hover:text-primary transition-colors ${locale === 'en' ? 'font-decorative' : ''}`}
              >
                <menuItem.icon className="h-4 w-4 opacity-70" />
                <span>{menuItem.label}</span>
              </Link>
            ))}
          </div>

          <div className="flex flex-1 items-center justify-end gap-3">
            <ColorModeToggle />
            <LocaleSwitch />

            <Sheet
              open={mobileMenuOpen}
              onOpenChange={(open) => setMobileMenuOpen(open)}
            >
              <SheetTrigger asChild>
                <Button className="md:hidden" size="icon" variant="outline">
                  <Icon.menu />
                </Button>
              </SheetTrigger>
              <SheetContent className="w-[250px]" side="right">
                <div className="flex flex-col items-start justify-center">
                  {menuItems.map((menuItem) => (
                    <Link
                      key={menuItem.href}
                      href={menuItem.href}
                      className={`flex items-center gap-2 px-3 py-2 text-lg font-medium hover:text-primary transition-colors ${locale === 'en' ? 'font-decorative' : ''}`}
                    >
                      <menuItem.icon className="h-4 w-4 opacity-70" />
                      <span>{menuItem.label}</span>
                    </Link>
                  ))}

                  <Link
                    key={user ? "dashboard" : "login"}
                    href={user ? `/app` : "/auth/login"}
                    className="flex items-center gap-2 px-3 py-2 text-lg"
                    prefetch={!user}
                  >
                    {user ? (
                      <>
                        <Icon.grid className="h-4 w-4 opacity-70" />
                        <span>{t("common.menu.dashboard")}</span>
                      </>
                    ) : (
                      <>
                        <Icon.user className="h-4 w-4 opacity-70" />
                        <span>{t("common.menu.login")}</span>
                      </>
                    )}
                  </Link>
                </div>
              </SheetContent>
            </Sheet>

            {isClient && userLoaded && (
              <>
                {user ? (
                  <Button
                    key="dashboard"
                    className="hidden md:block"
                    asChild
                    variant="ghost"
                  >
                    <Link href="/app">{t("common.menu.dashboard")}</Link>
                  </Button>
                ) : (
                  <Button
                    key="login"
                    className="hidden md:block"
                    asChild
                    variant="ghost"
                  >
                    <Link href="/auth/login" className="flex items-center gap-2">
                      <Icon.user className="h-4 w-4 opacity-70" />
                      <span>{t("common.menu.login")}</span>
                    </Link>
                  </Button>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </nav>
  );
}
