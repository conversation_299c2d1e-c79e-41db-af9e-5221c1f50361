import { Logo } from "@shared/components/Logo";
import { <PERSON> } from "@i18n";
import { useTranslations } from "next-intl";

export function Footer() {
  const t = useTranslations();
  return (
    <footer className="bg-muted text-muted-foreground py-12">
      <div className="container grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div>
          <Logo />
          <p className="mt-3 text-sm opacity-70">
            {t("footer.copyright", { year: new Date().getFullYear() })}
          </p>
        </div>

        <div className="flex flex-col gap-2">
          <Link href="/blog" className="block">
            {t("common.menu.blog")}
          </Link>

          <a href="#" className="block">
            {t("common.menu.features")}
          </a>

          <a href="#" className="block">
            {t("common.menu.pricing")}
          </a>
        </div>

        <div className="flex flex-col gap-2">
          <Link href="/privacy" className="block">
            {t("footer.legal.privacy")}
          </Link>

          <Link href="/terms" className="block">
            {t("footer.legal.terms")}
          </Link>

          <Link href="/contact" className="block">
            {t("footer.legal.contact")}
          </Link>
        </div>
      </div>
    </footer>
  );
}
