import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@ui/components/accordion";
import { Badge } from "@ui/components/badge";
import { getTranslations } from "next-intl/server";

export async function FAQ() {
  const t = await getTranslations();

  const items = [
    {
      q: t("home.faq.items.1.question"),
      a: t("home.faq.items.1.answer"),
    },
    {
      q: t("home.faq.items.2.question"),
      a: t("home.faq.items.2.answer"),
    },
    {
      q: t("home.faq.items.3.question"),
      a: t("home.faq.items.3.answer"),
    },
    {
      q: t("home.faq.items.4.question"),
      a: t("home.faq.items.4.answer"),
    },
    {
      q: t("home.faq.items.5.question"),
      a: t("home.faq.items.5.answer"),
    },
    {
      q: t("home.faq.items.6.question"),
      a: t("home.faq.items.6.answer"),
    },
  ];

  const mid = Math.ceil(items.length / 2);
  const left = items.slice(0, mid);
  const right = items.slice(mid);

  return (
    <section className=" border-t py-24">
      <div className="container">
        <div className="mb-12 text-center">
          <h2 className="heading-lg">{t("home.faq.title")}</h2>
          <p className="text-body-lg mt-4 opacity-70 max-w-2xl mx-auto">
            {/* optional subtitle key if needed: home.faq.subtitle */}
          </p>
        </div>

        <div className="grid grid-cols-1 gap-8 lg:grid-cols-2">
          <div>
            <Accordion type="single" collapsible className="w-full">
              {left.map((item, idx) => (
                <AccordionItem key={`left-${idx}`} value={`left-${idx}`}>
                  <AccordionTrigger>
                    <span className="flex items-center gap-3">
                      <Badge
                        status="info"
                        className="inline-flex size-6 items-center justify-center rounded-full px-0 py-0 text-[11px]"
                        aria-hidden
                      >
                        {idx + 1}
                      </Badge>
                      {item.q}
                    </span>
                  </AccordionTrigger>
                  <AccordionContent>{item.a}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
          <div>
            <Accordion type="single" collapsible className="w-full">
              {right.map((item, idx) => (
                <AccordionItem key={`right-${idx}`} value={`right-${idx}`}>
                  <AccordionTrigger>
                    <span className="flex items-center gap-3">
                      <Badge
                        status="info"
                        className="inline-flex size-6 items-center justify-center rounded-full px-0 py-0 text-[11px]"
                        aria-hidden
                      >
                        {mid + idx + 1}
                      </Badge>
                      {item.q}
                    </span>
                  </AccordionTrigger>
                  <AccordionContent>{item.a}</AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </div>
        </div>
      </div>
    </section>
  );
}