import Image from "next/image";

import { Button } from "@ui/components/button";
import { Icon } from "@ui/components/icon";
import { useTranslations } from "next-intl";
import heroDarkImage from "/public/images/hero-dark.svg";
import heroImage from "/public/images/hero.svg";

export function Features() {
  const t = useTranslations();
  return (
    <section className="bg-card text-card-foreground py-24">
      <div className="container">
        {/* Section header */}
        <div className="text-center">
          <h2 className="heading-lg">
            {t("home.features.title")}
          </h2>
          <p className="text-body-lg mt-4 opacity-70 max-w-2xl mx-auto">
            {t("home.features.subtitle")}
          </p>
        </div>

        <div className="mt-20 grid grid-cols-1 gap-16">
          {/* Feature 1 */}
          <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-16">
            <div className="bg-primary/10 rounded-2xl p-12">
              <Image
                src={heroImage}
                className="block dark:hidden"
                alt="Feature 1"
              />
              <Image
                src={heroDarkImage}
                className="hidden dark:block"
                alt="Feature 1"
              />
            </div>

            <div>
              <h3 className="heading-md">{t("home.features.items.a.title")}</h3>
              <p className="text-body mt-3 opacity-70">
                {t("home.features.items.a.description")}
              </p>
              <Button variant="link" size="sm" className="mt-4 px-0">
                {t("home.features.learnMore")} &rarr;
              </Button>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="bg-card text-card-foreground  rounded-xl border p-4">
                  <Icon.star className="text-primary h-6 w-6 text-3xl" />
                  <strong className="mt-2 block">{t("home.features.items.a.benefits.1.title")}</strong>
                  <p className="opacity-70">{t("home.features.items.a.benefits.1.description")}</p>
                </div>
                <div className="bg-card text-card-foreground  rounded-xl border p-4">
                  <Icon.pointer className="text-primary h-6 w-6 text-3xl" />
                  <strong className="mt-2 block">{t("home.features.items.a.benefits.2.title")}</strong>
                  <p className="opacity-70">{t("home.features.items.a.benefits.2.description")}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Feature 2 */}
          <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-16">
            <div className="bg-primary/10 rounded-2xl p-12 lg:order-2">
              <Image
                src={heroImage}
                className="block dark:hidden"
                alt="Feature 2"
              />
              <Image
                src={heroDarkImage}
                className="hidden dark:block"
                alt="Feature 2"
              />
            </div>

            <div className="lg:order-1">
              <h3 className="heading-md">{t("home.features.items.b.title")}</h3>
              <p className="text-body mt-3 opacity-70">
                {t("home.features.items.b.description")}
              </p>
              <Button variant="link" size="sm" className="mt-4 px-0">
                {t("home.features.learnMore")} &rarr;
              </Button>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="bg-card text-card-foreground  rounded-xl border p-4">
                  <Icon.upload className="text-primary h-6 w-6 text-3xl" />
                  <strong className="mt-2 block">{t("home.features.items.b.benefits.1.title")}</strong>
                  <p className="opacity-70">{t("home.features.items.b.benefits.1.description")}</p>
                </div>
                <div className="bg-card text-card-foreground  rounded-xl border p-4">
                  <Icon.cloud className="text-primary h-6 w-6 text-3xl" />
                  <strong className="mt-2 block">{t("home.features.items.b.benefits.2.title")}</strong>
                  <p className="opacity-70">{t("home.features.items.b.benefits.2.description")}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Feature 3 */}
          <div className="grid items-center gap-8 lg:grid-cols-2 lg:gap-16">
            <div className="bg-primary/10 rounded-2xl p-12 ">
              <Image
                src={heroImage}
                className="block dark:hidden"
                alt="Feature 3"
              />
              <Image
                src={heroDarkImage}
                className="hidden dark:block"
                alt="Feature 3"
              />
            </div>

            <div>
              <h3 className="heading-md">{t("home.features.items.c.title")}</h3>
              <p className="text-body mt-3 opacity-70">
                {t("home.features.items.c.description")}
              </p>
              <Button variant="link" size="sm" className="mt-4 px-0">
                {t("home.features.learnMore")} &rarr;
              </Button>

              <div className="mt-6 grid grid-cols-2 gap-4">
                <div className="bg-card text-card-foreground  rounded-xl border p-4">
                  <Icon.phone className="text-primary h-6 w-6 text-3xl" />
                  <strong className="mt-2 block">{t("home.features.items.c.benefits.1.title")}</strong>
                  <p className="opacity-70">{t("home.features.items.c.benefits.1.description")}</p>
                </div>
                <div className="bg-card text-card-foreground  rounded-xl border p-4">
                  <Icon.paperclip className="text-primary h-6 w-6 text-3xl" />
                  <strong className="mt-2 block">{t("home.features.items.c.benefits.2.title")}</strong>
                  <p className="opacity-70">{t("home.features.items.c.benefits.2.description")}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
