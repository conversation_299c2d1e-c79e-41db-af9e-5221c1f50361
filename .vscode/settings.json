{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.organizeImports": "explicit"}, "tailwindCSS.experimental.classRegex": [["tv\\(([^)]*)\\)", "[\"'`]([^\"'`]*).*?[\"'`]"]], "typescript.preferences.importModuleSpecifier": "shortest", "typescript.tsdk": "node_modules/typescript/lib", "typescript.enablePromptUseWorkspaceTsdk": true, "eslint.workingDirectories": [{"pattern": "apps/*/"}, "packages/api", "packages/mail", "packages/database", "packages/config/eslint-config-custom", "packages/config/tailwind-config", "packages/config/tsconfig"], "i18n-ally.localesPaths": ["apps/web/locales"], "i18n-ally.keystyle": "nested", "i18n-ally.enabledFrameworks": ["next-intl"]}