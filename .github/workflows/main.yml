name: e2e

on: push

jobs:
  e2e:
    name: E2E tests
    runs-on: ubuntu-22.04
    steps:
      - name: Checkout
        uses: actions/checkout@v4
      - uses: pnpm/action-setup@v2
        with:
          version: 8
      - name: Install cypress
        uses: cypress-io/github-action@v5
        with:
          runTests: false
      - run: pnpm build
      - name: Cypress run
        uses: cypress-io/github-action@v6
        with:
          install: false
          working-directory: apps/web
          start: pnpm start
          wait-on: "http://localhost:3000"
        env:
          NEXT_PUBLIC_SITE_URL: ${{secrets.NEXT_PUBLIC_SITE_URL}}
          DATABASE_URL: ${{secrets.DATABASE_URL}}
          STRIPE_SECRET_KEY: ${{secrets.STRIPE_SECRET_KEY}}
